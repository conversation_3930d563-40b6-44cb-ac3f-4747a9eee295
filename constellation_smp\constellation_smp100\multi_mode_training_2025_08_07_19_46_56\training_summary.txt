🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_07_19_46_56
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 64
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_07_19_47_00
使用模型: gpn_transformer
Actor参数数量: 178,724
Critic参数数量: 87,201

开始训练 Epoch 1/3
❌ 模式 COOPERATIVE 训练失败: The size of tensor a (3) must match the size of tensor b (100) at non-singleton dimension 2
错误详情:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\train_constellation.py", line 355, in train_constellation_smp
    save_dir, avg_reward, revenue_rate = train_single_constellation_mode(mode)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\train_constellation.py", line 272, in train_single_constellation_mode
    train_constellation_smp_process(actor, critic, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\train_constellation.py", line 473, in train_constellation_smp_process
    tour_indices, satellite_indices, tour_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\constellation_smp\gpn_transformer.py", line 180, in forward
    task_logits = self.task_pointer(current_state, constellation_features, mask)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\constellation_smp\gpn_transformer.py", line 82, in forward
    similarity = similarity.masked_fill(mask == 0, -1e9)
RuntimeError: The size of tensor a (3) must match the size of tensor b (100) at non-singleton dimension 2


🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 64
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_07_19_47_10
使用模型: gpn_transformer
Actor参数数量: 178,724
Critic参数数量: 87,201

开始训练 Epoch 1/3
❌ 模式 COMPETITIVE 训练失败: The size of tensor a (3) must match the size of tensor b (100) at non-singleton dimension 2
错误详情:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\train_constellation.py", line 355, in train_constellation_smp
    save_dir, avg_reward, revenue_rate = train_single_constellation_mode(mode)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\train_constellation.py", line 272, in train_single_constellation_mode
    train_constellation_smp_process(actor, critic, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\train_constellation.py", line 473, in train_constellation_smp_process
    tour_indices, satellite_indices, tour_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\constellation_smp\gpn_transformer.py", line 180, in forward
    task_logits = self.task_pointer(current_state, constellation_features, mask)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0801single_smp\constellation_smp\gpn_transformer.py", line 82, in forward
    similarity = similarity.masked_fill(mask == 0, -1e9)
RuntimeError: The size of tensor a (3) must match the size of tensor b (100) at non-singleton dimension 2


🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
